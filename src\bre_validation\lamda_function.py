import os
import json
from typing import Dict, <PERSON><PERSON>, List
from validation_helper.validation_execution import ValidatorExecution
from validation_helper.core.validation_engine import ValidationEngine

# Import the same utilities as BRE handler for consistency
import sys
sys.path.append('/opt/python')  # For Lambda layers
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'bre_handler'))

from aria_helper.crud_statuses import CrudStatuses
from aria_helper.crud_handler_execution import <PERSON><PERSON><PERSON><PERSON><PERSON>
from aria_helper.boto3_utils import trigger_lambda, trigger_lambda_response, get_secret
from aria_helper.mongo_utils import Mongo
from aria_helper.aria_utils import ARIA

environment = os.environ['ENV']
database_name = os.environ['DATABASE_NAME']
aria_environment = os.environ['ARIA_ENVIRONMENT']


class DatabaseClient:
    """Database client for BRE validation using the same Mongo utilities as BRE handler"""

    def __init__(self, mongo_client: Mongo = None):
        self.mongo_client = mongo_client or Mongo(get_secret(environment + '-mongodb_uri', return_json=False))

    def get_config(self, key: str) -> Dict:
        """Get validation configuration from database"""
        self.mongo_client.select_db_and_collection(database_name, collection_name="validation_config")
        config = self.mongo_client.find_one({"key": key})
        return config.get("config", {}) if config else {}

    def save_validation_result(self, app_id: str, document_id: str, validation_result: Dict):
        """Save validation results to database"""
        self.mongo_client.select_db_and_collection(database_name, collection_name="validation_results")
        result_data = {
            "app_id": app_id,
            "document_id": document_id,
            "validation_result": validation_result,
            "timestamp": json.dumps({"$date": {"$numberLong": str(int(os.times().elapsed * 1000))}})
        }
        self.mongo_client.insert_or_update(
            filter={"app_id": app_id, "document_id": document_id},
            data=result_data
        )


class BreValidationHandler:
    """BRE Validation Handler following the same pattern as BreHandler"""

    def __init__(self, event):
        self.event = event
        self.input_body = json.loads(event['body'])
        self.action_data = self.input_body.get('action', {})
        self.document = self.input_body['document']
        self.app_id = self.document['app_id']
        self.statuses = self.input_body.get('status', {})

        # Initialize database connections using same pattern as BRE handler
        self.mongo_client = Mongo(get_secret(environment + '-mongodb_uri', return_json=False))
        self.aria_secret = get_secret(secret_name=f'{environment}-aria_cm_tokens')
        self.crud_handler = CrudHandler(self.mongo_client)
        self.crud_statuses = CrudStatuses(self.mongo_client)

        # Initialize validation components
        self.db_client = DatabaseClient(self.mongo_client)
        self.validator_execution = ValidatorExecution(self.db_client)

        self._set_validation_config_by_app_id()

    def _set_validation_config_by_app_id(self):
        """Get validation configuration for the app_id"""
        self.mongo_client.select_db_and_collection(database_name, collection_name="validation_config")
        self.validation_config = self.mongo_client.find_one({"app_id": self.app_id})

    def handle_document_without_group(self):
        """Handle documents without OCR groups - similar to BRE handler"""
        aria_exception = "No OCR. Cannot execute validation rules"
        target_status_id = [k for k, v in self.statuses.items() if 'needs' in v['label'].lower()]
        if len(target_status_id) == 0:
            raise ValueError("No target status")

        aria = ARIA(base_url=self.aria_secret[aria_environment]['url'],
                    request_token=self.aria_secret[aria_environment]['token'])
        aria.bre_reply(
            app_id=self.document['app_id'],
            item_id=self.document['id'],
            bre_response={
                "aria_status": {"value": target_status_id},
                "aria_exception": {"value": aria_exception}
            })

    def validate_document_data(self) -> Tuple[bool, Dict]:
        """Validate document data using the validation engine"""
        try:
            # Get validation configuration
            if not self.validation_config:
                return False, {"error": f"No validation configuration found for app_id: {self.app_id}"}

            config_key = self.validation_config.get("config_key", "tag_titles")
            config_data = self.db_client.get_config(config_key)

            if not config_data:
                return False, {"error": f"Configuration not found for key: {config_key}"}

            # Extract document data for validation
            document_data = self.document.copy()

            # Perform validation
            is_valid, validated_data = self.validator_execution.validate_data(document_data, config_data)

            # Save validation results
            self.db_client.save_validation_result(
                self.app_id,
                self.document['id'],
                {
                    "is_valid": is_valid,
                    "validated_data": validated_data,
                    "config_key": config_key
                }
            )

            return is_valid, validated_data

        except Exception as e:
            error_msg = f"Validation error: {str(e)}"
            return False, {"error": error_msg}

    def next_step(self):
        """
        Decides what lambda calls after validation, following the same pattern as BRE handler
        """
        next_function = None
        lambda_trigger_type = None
        bre_type = "validation"
        request_response = None
        action_name = self.action_data.get('action_label', None)
        if action_name:
            action_name = action_name.lower()

        print("VALIDATION ACTION TO DO", action_name)
        self.crud_statuses.insert_or_update(self.app_id, self.statuses)

        if action_name is None:
            if len(self.document.get('ocr_groups', [])) == 0:
                self.handle_document_without_group()
                bre_type = "document_without_group"
                return bre_type, None, None, None

            # For validation, we might want to trigger a different lambda after validation
            # This could be configured in the database similar to BRE handler
            self.mongo_client.select_db_and_collection(database_name, collection_name="validation_config")
            validation_config = self.mongo_client.find_one({"app_id": self.app_id})

            if validation_config and validation_config.get("next_function"):
                next_function = os.environ.get(validation_config.get("next_function"))
                lambda_trigger_type = trigger_lambda
                request_response = False

        else:
            # Handle specific validation actions
            self.mongo_client.select_db_and_collection(database_name, collection_name="validation_action_config")
            actions_dict = self.mongo_client.find_one({"app_id": self.app_id})

            if actions_dict and action_name in actions_dict:
                action_vals = actions_dict[action_name]
                next_function = os.environ.get(action_vals.get('next_function'))
                request_response = action_vals.get('request_response', False)
                lambda_trigger_type = trigger_lambda_response if request_response else trigger_lambda

                if action_vals.get('request_response'):
                    bre_type = action_vals.get('bre_type', 'validation')

        return bre_type, next_function, lambda_trigger_type, request_response

    def run(self):
        """Main execution method following BRE handler pattern"""
        try:
            # First perform validation
            is_valid, validation_result = self.validate_document_data()

            if not is_valid:
                # If validation fails, we might want to send this back to ARIA
                aria = ARIA(base_url=self.aria_secret[aria_environment]['url'],
                           request_token=self.aria_secret[aria_environment]['token'])

                # Extract validation errors for ARIA response
                validation_errors = validation_result.get("error", "Validation failed")
                if isinstance(validation_result, dict) and "groups" in validation_result:
                    # Extract bre_exceptions from groups
                    errors = []
                    for group_name, group_data in validation_result.get("groups", {}).items():
                        if "bre_exceptions" in group_data:
                            for field, error in group_data["bre_exceptions"].items():
                                errors.append(f"{group_name}.{field}: {error}")

                    # Also check for group-level exceptions
                    if "group_top_exceptions" in validation_result:
                        for rule_id, error in validation_result["group_top_exceptions"].items():
                            errors.append(f"Rule {rule_id}: {error}")

                    validation_errors = "; ".join(errors) if errors else "Validation failed"

                # Find target status for validation failure
                target_status_id = [k for k, v in self.statuses.items() if 'needs' in v['label'].lower()]
                if target_status_id:
                    aria.bre_reply(
                        app_id=self.document['app_id'],
                        item_id=self.document['id'],
                        bre_response={
                            "aria_status": {"value": target_status_id},
                            "aria_exception": {"value": validation_errors}
                        })

                return {
                    'statusCode': 400,
                    'body': json.dumps({
                        'message': 'Validation failed',
                        'validation_result': validation_result
                    })
                }

            # If validation passes, determine next step
            bre_type, next_function, method, request_response = self.next_step()

            if bre_type == "document_without_group":
                return {
                    'statusCode': 200,
                    'body': json.dumps({'message': 'No OCR to process'})
                }

            # Save execution record
            execution_id = self.crud_handler.insert_execution(self.input_body, next_function or 'validation_complete')

            # Update the information on the input_body
            self.input_body['bre_type'] = bre_type
            self.input_body['execution_id'] = execution_id
            self.input_body['request_response'] = request_response
            self.input_body['validation_result'] = validation_result

            # If there's a next function to call, trigger it
            if next_function and method:
                try:
                    lambda_response = method(next_function, self.input_body)
                except Exception as e:
                    self.crud_handler.mark_as_failed(execution_id, error_message=str(e))
                    return {
                        'statusCode': 500,
                        'body': json.dumps({'message': 'Issue while triggering next lambda: ' + str(e)})
                    }

                # If the lambda is synchronous, wait for its response
                if method == trigger_lambda_response:
                    response_payload = json.loads(lambda_response['Payload'].read())
                    print("RESPONSE FROM LAMBDA ->> ", response_payload)

                    return {
                        'statusCode': response_payload.get('statusCode'),
                        'body': response_payload.get('body')
                    }

            # Validation completed successfully
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'Validation completed successfully',
                    'validation_result': validation_result
                })
            }

        except Exception as e:
            self.crud_handler.insert_execution(self.event, 'None', failed=True, error_message=str(e))
            return {
                'statusCode': 500,
                'body': json.dumps({'message': 'Issue while processing validation: ' + str(e)})
            }


def lambda_handler(event, context):
    """Lambda handler entry point"""
    print("BRE Validation Event:", event)
    if 'body' not in list(event.keys()):
        raise ValueError("body tag is missing on the dict. Skipping...")

    bre_validation_handler = BreValidationHandler(event)
    return bre_validation_handler.run()