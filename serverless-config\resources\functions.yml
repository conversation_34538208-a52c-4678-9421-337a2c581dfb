functions:
  bre_handler:
    name: ${self:custom.servicePrefix}-bre_handler
    handler: src/bre_handler/lambda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    role: BreHandlerRole
    layers:
      - { Ref: PyMongoAWSLayerLambdaLayer }
      - { Ref: Boto3LayerLambdaLayer }
      - { Ref: RequestsLayerLambdaLayer }
    package:
      individually: true
      patterns:
        - "!**/*"
        - src/bre_handler/**
    events:
      - httpApi:
          path: /${self:custom.servicePrefix}/bre_handler
          method: post

  bre_validation:
    name: ${self:custom.servicePrefix}-bre_validation
    handler: src/bre_validation/lamda_function.lambda_handler
    runtime: python3.11
    architecture: x86_64
    memorySize: 256
    timeout: 900
    ephemeralStorageSize: 512
    role: BreValidationRole
    layers:
      - { Ref: PyMongoAWSLayerLambdaLayer }
      - { Ref: Boto3LayerLambdaLayer }
      - { Ref: RequestsLayerLambdaLayer }
    package:
      individually: true
      patterns:
        - "!**/*"
        - src/bre_validation/**
    events:
      - httpApi:
          path: /${self:custom.servicePrefix}/bre_validation
          method: post